'use client';

import { motion } from 'framer-motion';
import { GraduationCap, Award, Calendar, MapPin, BookOpen } from 'lucide-react';

const EducationSection = () => {
  const education = [
    {
      degree: "M.Sc Biotechnology",
      institution: "St. Thomas College, Bhilai",
      period: "2022 – 2024",
      grade: "70%",
      location: "Bhilai, India",
      description: "Advanced studies in biotechnology with focus on research methodologies, environmental applications, and laboratory techniques.",
      highlights: [
        "Specialized in environmental biotechnology",
        "Conducted independent research projects",
        "Advanced laboratory training",
        "Research methodology and data analysis"
      ]
    },
    {
      degree: "B.Sc Biotechnology",
      institution: "St. Thomas College, Bhilai",
      period: "2019 – 2022",
      grade: "73.6%",
      location: "Bhilai, India",
      description: "Comprehensive undergraduate program covering fundamental principles of biotechnology, microbiology, and biochemistry.",
      highlights: [
        "Strong foundation in biotechnology principles",
        "Microbiology and biochemistry expertise",
        "Laboratory skills development",
        "Academic excellence recognition"
      ]
    }
  ];

  const certifications = [
    {
      title: "ICMR-ROHC Research Facility Training",
      issuer: "DST-SERB",
      year: "2024",
      description: "Comprehensive training in research methodologies and laboratory techniques at ICMR-ROHC facility"
    },
    {
      title: "International Workshop on Data Science & ML in R",
      issuer: "International Organization",
      year: "2024",
      description: "Advanced training in data science applications and machine learning using R programming"
    },
    {
      title: "International Workshop on Data Analytics in Biological Research",
      issuer: "International Organization",
      year: "2024",
      description: "Specialized training in applying data analytics techniques to biological research problems"
    }
  ];

  return (
    <section className="section-padding bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Education & <span className="text-gradient">Certifications</span>
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-blue-900 to-teal-400 mx-auto mb-8"></div>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Academic excellence and continuous learning in biotechnology
          </p>
        </motion.div>

        {/* Education Section */}
        <div className="mb-16">
          <motion.h3
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-2xl font-bold text-gray-900 mb-8 flex items-center"
          >
            <GraduationCap className="w-8 h-8 text-teal-500 mr-3" />
            Academic Qualifications
          </motion.h3>

          <div className="space-y-8">
            {education.map((edu, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                viewport={{ once: true }}
                className="card-hover bg-white rounded-xl p-6 shadow-lg border border-gray-100"
              >
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {/* Left Column - Main Info */}
                  <div className="lg:col-span-2">
                    <div className="flex items-start justify-between mb-4">
                      <div>
                        <h4 className="text-xl font-bold text-gray-900 mb-2">
                          {edu.degree}
                        </h4>
                        <p className="text-teal-600 font-semibold text-lg mb-2">
                          {edu.institution}
                        </p>
                        <div className="flex flex-col sm:flex-row sm:items-center text-gray-600 text-sm space-y-1 sm:space-y-0 sm:space-x-4">
                          <div className="flex items-center">
                            <Calendar size={14} className="mr-1" />
                            {edu.period}
                          </div>
                          <div className="flex items-center">
                            <MapPin size={14} className="mr-1" />
                            {edu.location}
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="bg-gradient-to-r from-blue-900 to-teal-400 text-white px-4 py-2 rounded-lg">
                          <span className="text-sm font-medium">Grade</span>
                          <div className="text-xl font-bold">{edu.grade}</div>
                        </div>
                      </div>
                    </div>

                    <p className="text-gray-700 mb-4 leading-relaxed">
                      {edu.description}
                    </p>

                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                      {edu.highlights.map((highlight, idx) => (
                        <div key={idx} className="flex items-center text-sm text-gray-600">
                          <span className="w-2 h-2 bg-teal-400 rounded-full mr-2"></span>
                          {highlight}
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Right Column - Visual Element */}
                  <div className="flex items-center justify-center">
                    <div className="w-32 h-32 bg-gradient-to-br from-blue-100 to-teal-100 rounded-full flex items-center justify-center">
                      <GraduationCap className="w-16 h-16 text-teal-600" />
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Certifications Section */}
        <div>
          <motion.h3
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-2xl font-bold text-gray-900 mb-8 flex items-center"
          >
            <Award className="w-8 h-8 text-teal-500 mr-3" />
            Certifications & Workshops
          </motion.h3>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {certifications.map((cert, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="card-hover bg-white rounded-xl p-6 shadow-lg border border-gray-100"
              >
                <div className="text-center mb-4">
                  <div className="w-16 h-16 bg-gradient-to-r from-blue-900 to-teal-400 rounded-full flex items-center justify-center mx-auto mb-4">
                    <BookOpen className="w-8 h-8 text-white" />
                  </div>
                  <h4 className="text-lg font-bold text-gray-900 mb-2">
                    {cert.title}
                  </h4>
                  <p className="text-teal-600 font-semibold mb-1">
                    {cert.issuer}
                  </p>
                  <p className="text-gray-500 text-sm mb-3">
                    {cert.year}
                  </p>
                </div>
                <p className="text-gray-600 text-sm leading-relaxed text-center">
                  {cert.description}
                </p>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default EducationSection;
