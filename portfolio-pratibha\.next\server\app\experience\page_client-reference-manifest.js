globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/experience/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./src/components/Footer.tsx":{"*":{"id":"(ssr)/./src/components/Footer.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Navigation.tsx":{"*":{"id":"(ssr)/./src/components/Navigation.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/AboutSection.tsx":{"*":{"id":"(ssr)/./src/components/AboutSection.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/EducationSection.tsx":{"*":{"id":"(ssr)/./src/components/EducationSection.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ExperienceSection.tsx":{"*":{"id":"(ssr)/./src/components/ExperienceSection.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/HeroSection.tsx":{"*":{"id":"(ssr)/./src/components/HeroSection.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ProjectsSection.tsx":{"*":{"id":"(ssr)/./src/components/ProjectsSection.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/SkillsSection.tsx":{"*":{"id":"(ssr)/./src/components/SkillsSection.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/builtin/global-error.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/builtin/global-error.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/lib/framework/boundary-components.js":{"*":{"id":"(ssr)/./node_modules/next/dist/lib/framework/boundary-components.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js":{"*":{"id":"(ssr)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js":{"*":{"id":"(ssr)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/contact/page.tsx":{"*":{"id":"(ssr)/./src/app/contact/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"D:\\Oshi-Pari\\portfolio-oshi\\portfolio-pratibha\\src\\app\\globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Oshi-Pari\\portfolio-oshi\\portfolio-pratibha\\src\\components\\Footer.tsx":{"id":"(app-pages-browser)/./src/components/Footer.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Oshi-Pari\\portfolio-oshi\\portfolio-pratibha\\src\\components\\Navigation.tsx":{"id":"(app-pages-browser)/./src/components/Navigation.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\Oshi-Pari\\portfolio-oshi\\portfolio-pratibha\\src\\components\\AboutSection.tsx":{"id":"(app-pages-browser)/./src/components/AboutSection.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"D:\\Oshi-Pari\\portfolio-oshi\\portfolio-pratibha\\src\\components\\EducationSection.tsx":{"id":"(app-pages-browser)/./src/components/EducationSection.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"D:\\Oshi-Pari\\portfolio-oshi\\portfolio-pratibha\\src\\components\\ExperienceSection.tsx":{"id":"(app-pages-browser)/./src/components/ExperienceSection.tsx","name":"*","chunks":["app/experience/page","static/chunks/app/experience/page.js"],"async":false},"D:\\Oshi-Pari\\portfolio-oshi\\portfolio-pratibha\\src\\components\\HeroSection.tsx":{"id":"(app-pages-browser)/./src/components/HeroSection.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"D:\\Oshi-Pari\\portfolio-oshi\\portfolio-pratibha\\src\\components\\ProjectsSection.tsx":{"id":"(app-pages-browser)/./src/components/ProjectsSection.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"D:\\Oshi-Pari\\portfolio-oshi\\portfolio-pratibha\\src\\components\\SkillsSection.tsx":{"id":"(app-pages-browser)/./src/components/SkillsSection.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"D:\\Oshi-Pari\\portfolio-oshi\\portfolio-pratibha\\node_modules\\next\\dist\\client\\components\\builtin\\global-error.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/builtin/global-error.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Oshi-Pari\\portfolio-oshi\\portfolio-pratibha\\node_modules\\next\\dist\\esm\\client\\components\\builtin\\global-error.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/builtin/global-error.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Oshi-Pari\\portfolio-oshi\\portfolio-pratibha\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Oshi-Pari\\portfolio-oshi\\portfolio-pratibha\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Oshi-Pari\\portfolio-oshi\\portfolio-pratibha\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Oshi-Pari\\portfolio-oshi\\portfolio-pratibha\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Oshi-Pari\\portfolio-oshi\\portfolio-pratibha\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Oshi-Pari\\portfolio-oshi\\portfolio-pratibha\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Oshi-Pari\\portfolio-oshi\\portfolio-pratibha\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Oshi-Pari\\portfolio-oshi\\portfolio-pratibha\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Oshi-Pari\\portfolio-oshi\\portfolio-pratibha\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Oshi-Pari\\portfolio-oshi\\portfolio-pratibha\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Oshi-Pari\\portfolio-oshi\\portfolio-pratibha\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Oshi-Pari\\portfolio-oshi\\portfolio-pratibha\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Oshi-Pari\\portfolio-oshi\\portfolio-pratibha\\node_modules\\next\\dist\\lib\\framework\\boundary-components.js":{"id":"(app-pages-browser)/./node_modules/next/dist/lib/framework/boundary-components.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Oshi-Pari\\portfolio-oshi\\portfolio-pratibha\\node_modules\\next\\dist\\esm\\lib\\framework\\boundary-components.js":{"id":"(app-pages-browser)/./node_modules/next/dist/lib/framework/boundary-components.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Oshi-Pari\\portfolio-oshi\\portfolio-pratibha\\node_modules\\next\\dist\\lib\\metadata\\generate\\icon-mark.js":{"id":"(app-pages-browser)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Oshi-Pari\\portfolio-oshi\\portfolio-pratibha\\node_modules\\next\\dist\\esm\\lib\\metadata\\generate\\icon-mark.js":{"id":"(app-pages-browser)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Oshi-Pari\\portfolio-oshi\\portfolio-pratibha\\node_modules\\next\\dist\\next-devtools\\userspace\\app\\segment-explorer-node.js":{"id":"(app-pages-browser)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Oshi-Pari\\portfolio-oshi\\portfolio-pratibha\\node_modules\\next\\dist\\esm\\next-devtools\\userspace\\app\\segment-explorer-node.js":{"id":"(app-pages-browser)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\Oshi-Pari\\portfolio-oshi\\portfolio-pratibha\\src\\app\\contact\\page.tsx":{"id":"(app-pages-browser)/./src/app/contact/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"D:\\Oshi-Pari\\portfolio-oshi\\portfolio-pratibha\\src\\":[],"D:\\Oshi-Pari\\portfolio-oshi\\portfolio-pratibha\\src\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"D:\\Oshi-Pari\\portfolio-oshi\\portfolio-pratibha\\src\\app\\page":[],"D:\\Oshi-Pari\\portfolio-oshi\\portfolio-pratibha\\src\\app\\experience\\page":[]},"rscModuleMapping":{"(app-pages-browser)/./src/app/globals.css":{"*":{"id":"(rsc)/./src/app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Footer.tsx":{"*":{"id":"(rsc)/./src/components/Footer.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Navigation.tsx":{"*":{"id":"(rsc)/./src/components/Navigation.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/AboutSection.tsx":{"*":{"id":"(rsc)/./src/components/AboutSection.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/EducationSection.tsx":{"*":{"id":"(rsc)/./src/components/EducationSection.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ExperienceSection.tsx":{"*":{"id":"(rsc)/./src/components/ExperienceSection.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/HeroSection.tsx":{"*":{"id":"(rsc)/./src/components/HeroSection.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ProjectsSection.tsx":{"*":{"id":"(rsc)/./src/components/ProjectsSection.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/SkillsSection.tsx":{"*":{"id":"(rsc)/./src/components/SkillsSection.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/builtin/global-error.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/builtin/global-error.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/lib/framework/boundary-components.js":{"*":{"id":"(rsc)/./node_modules/next/dist/lib/framework/boundary-components.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js":{"*":{"id":"(rsc)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js":{"*":{"id":"(rsc)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/contact/page.tsx":{"*":{"id":"(rsc)/./src/app/contact/page.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}