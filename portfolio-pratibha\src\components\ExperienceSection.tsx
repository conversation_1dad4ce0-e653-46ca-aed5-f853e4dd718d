'use client';

import { motion } from 'framer-motion';
import { Calendar, MapPin, Building } from 'lucide-react';

const ExperienceSection = () => {
  const experiences = [
    {
      title: "Project Assistant",
      company: "ICMR-NIV, Bengaluru",
      period: "June 2025 – Present",
      location: "Bengaluru, India",
      type: "Full-time",
      description: [
        "Assisting in cutting-edge research projects focused on virology and public health",
        "Conducting comprehensive data analysis and maintaining detailed research documentation",
        "Collaborating with multidisciplinary teams on innovative biotechnology solutions",
        "Contributing to research publications and scientific presentations"
      ],
      skills: ["Research", "Data Analysis", "Virology", "Documentation", "Team Collaboration"]
    },
    {
      title: "Research Intern",
      company: "ICMR-ROHC, Bengaluru",
      period: "January 2024 – June 2024",
      location: "Bengaluru, India",
      type: "Internship",
      description: [
        "Conducted medical and occupational health research under DST-SERB funding",
        "Gained hands-on experience with advanced laboratory techniques and equipment",
        "Participated in research facility training programs and workshops",
        "Contributed to ongoing research projects in environmental health"
      ],
      skills: ["Medical Research", "Occupational Health", "Laboratory Techniques", "Environmental Health"]
    },
    {
      title: "Academic Research Projects",
      company: "St. Thomas College, Bhilai",
      period: "2022 – 2024",
      location: "Bhilai, India",
      type: "Academic",
      description: [
        "Conducted independent research on plant tissue culture techniques",
        "Performed trace metal analysis using ELISA-ICP-OES integration",
        "Developed expertise in microbial culture maintenance and analysis",
        "Completed multiple research projects with focus on environmental biotechnology"
      ],
      skills: ["Plant Tissue Culture", "ELISA", "ICP-OES", "Microbial Culture", "Environmental Biotechnology"]
    }
  ];

  return (
    <section className="section-padding bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Professional <span className="text-gradient">Experience</span>
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-blue-900 to-teal-400 mx-auto mb-8"></div>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            A journey through research excellence and scientific innovation
          </p>
        </motion.div>

        <div className="relative">
          {/* Timeline Line */}
          <div className="absolute left-4 md:left-1/2 transform md:-translate-x-1/2 h-full w-0.5 bg-gradient-to-b from-blue-900 to-teal-400"></div>

          {experiences.map((experience, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.2 }}
              viewport={{ once: true }}
              className={`relative flex items-center mb-12 ${
                index % 2 === 0 ? 'md:flex-row' : 'md:flex-row-reverse'
              }`}
            >
              {/* Timeline Dot */}
              <div className="absolute left-4 md:left-1/2 transform md:-translate-x-1/2 w-4 h-4 bg-gradient-to-r from-blue-900 to-teal-400 rounded-full border-4 border-white shadow-lg z-10"></div>

              {/* Content Card */}
              <div className={`w-full md:w-5/12 ml-12 md:ml-0 ${index % 2 === 0 ? 'md:mr-auto md:pr-8' : 'md:ml-auto md:pl-8'}`}>
                <motion.div
                  whileHover={{ scale: 1.02 }}
                  className="card-hover bg-white rounded-xl p-6 shadow-lg border border-gray-100"
                >
                  {/* Header */}
                  <div className="mb-4">
                    <div className="flex items-center justify-between mb-2">
                      <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                        experience.type === 'Full-time' ? 'bg-green-100 text-green-800' :
                        experience.type === 'Internship' ? 'bg-blue-100 text-blue-800' :
                        'bg-purple-100 text-purple-800'
                      }`}>
                        {experience.type}
                      </span>
                    </div>
                    <h3 className="text-xl font-bold text-gray-900 mb-1">
                      {experience.title}
                    </h3>
                    <div className="flex items-center text-teal-600 font-semibold mb-2">
                      <Building size={16} className="mr-2" />
                      {experience.company}
                    </div>
                    <div className="flex flex-col sm:flex-row sm:items-center text-gray-600 text-sm space-y-1 sm:space-y-0 sm:space-x-4">
                      <div className="flex items-center">
                        <Calendar size={14} className="mr-1" />
                        {experience.period}
                      </div>
                      <div className="flex items-center">
                        <MapPin size={14} className="mr-1" />
                        {experience.location}
                      </div>
                    </div>
                  </div>

                  {/* Description */}
                  <ul className="space-y-2 mb-4">
                    {experience.description.map((item, idx) => (
                      <li key={idx} className="text-gray-700 text-sm flex items-start">
                        <span className="w-2 h-2 bg-teal-400 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                        {item}
                      </li>
                    ))}
                  </ul>

                  {/* Skills */}
                  <div className="flex flex-wrap gap-2">
                    {experience.skills.map((skill, idx) => (
                      <span
                        key={idx}
                        className="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-xs font-medium"
                      >
                        {skill}
                      </span>
                    ))}
                  </div>
                </motion.div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ExperienceSection;
