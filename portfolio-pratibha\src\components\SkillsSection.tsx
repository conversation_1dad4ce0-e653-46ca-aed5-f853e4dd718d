'use client';

import { motion } from 'framer-motion';
import { 
  FlaskConical, 
  Microscope, 
  Computer, 
  Users, 
  Dna, 
  BarChart3,
  FileSpreadsheet,
  Presentation,
  Clock,
  Target
} from 'lucide-react';

const SkillsSection = () => {
  const skillCategories = [
    {
      title: "Technical Skills",
      icon: <FlaskConical className="w-6 h-6" />,
      color: "from-blue-500 to-blue-600",
      skills: [
        { name: "ELISA", level: 90, icon: <Microscope className="w-4 h-4" /> },
        { name: "DNA/RNA Isolation", level: 85, icon: <Dna className="w-4 h-4" /> },
        { name: "ICP-OES", level: 80, icon: <BarChart3 className="w-4 h-4" /> },
        { name: "Plant Tissue Culture", level: 85, icon: <FlaskConical className="w-4 h-4" /> },
        { name: "Microbial Culture", level: 88, icon: <Microscope className="w-4 h-4" /> },
        { name: "Microscopy", level: 82, icon: <Microscope className="w-4 h-4" /> }
      ]
    },
    {
      title: "Software & Tools",
      icon: <Computer className="w-6 h-6" />,
      color: "from-teal-500 to-teal-600",
      skills: [
        { name: "MS Office Suite", level: 95, icon: <FileSpreadsheet className="w-4 h-4" /> },
        { name: "MS Excel", level: 90, icon: <BarChart3 className="w-4 h-4" /> },
        { name: "PowerPoint", level: 88, icon: <Presentation className="w-4 h-4" /> },
        { name: "R (Basic)", level: 70, icon: <Computer className="w-4 h-4" /> },
        { name: "Data Conversion", level: 85, icon: <BarChart3 className="w-4 h-4" /> }
      ]
    },
    {
      title: "Soft Skills",
      icon: <Users className="w-6 h-6" />,
      color: "from-purple-500 to-purple-600",
      skills: [
        { name: "Time Management", level: 92, icon: <Clock className="w-4 h-4" /> },
        { name: "Organization", level: 90, icon: <Target className="w-4 h-4" /> },
        { name: "Teamwork", level: 88, icon: <Users className="w-4 h-4" /> },
        { name: "Problem Solving", level: 85, icon: <Target className="w-4 h-4" /> },
        { name: "Communication", level: 87, icon: <Presentation className="w-4 h-4" /> }
      ]
    }
  ];

  return (
    <section className="section-padding bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Skills & <span className="text-gradient">Expertise</span>
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-blue-900 to-teal-400 mx-auto mb-8"></div>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            A comprehensive toolkit for biotechnology research and innovation
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {skillCategories.map((category, categoryIndex) => (
            <motion.div
              key={category.title}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: categoryIndex * 0.2 }}
              viewport={{ once: true }}
              className="card-hover bg-gradient-to-br from-gray-50 to-white rounded-xl p-6 shadow-lg border border-gray-100"
            >
              {/* Category Header */}
              <div className="flex items-center mb-6">
                <div className={`p-3 rounded-lg bg-gradient-to-r ${category.color} text-white mr-4`}>
                  {category.icon}
                </div>
                <h3 className="text-xl font-bold text-gray-900">
                  {category.title}
                </h3>
              </div>

              {/* Skills List */}
              <div className="space-y-4">
                {category.skills.map((skill, skillIndex) => (
                  <motion.div
                    key={skill.name}
                    initial={{ opacity: 0, x: -20 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    transition={{ 
                      duration: 0.6, 
                      delay: categoryIndex * 0.2 + skillIndex * 0.1 
                    }}
                    viewport={{ once: true }}
                    className="space-y-2"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <div className="text-gray-600">
                          {skill.icon}
                        </div>
                        <span className="text-gray-800 font-medium text-sm">
                          {skill.name}
                        </span>
                      </div>
                      <span className="text-gray-600 text-sm font-medium">
                        {skill.level}%
                      </span>
                    </div>
                    
                    {/* Progress Bar */}
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <motion.div
                        initial={{ width: 0 }}
                        whileInView={{ width: `${skill.level}%` }}
                        transition={{ 
                          duration: 1.5, 
                          delay: categoryIndex * 0.2 + skillIndex * 0.1 + 0.3,
                          ease: "easeOut"
                        }}
                        viewport={{ once: true }}
                        className={`h-2 rounded-full bg-gradient-to-r ${category.color}`}
                      />
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          ))}
        </div>

        {/* Additional Skills Highlight */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          viewport={{ once: true }}
          className="mt-16 text-center"
        >
          <div className="bg-gradient-to-r from-gray-900 to-gray-800 rounded-2xl p-8 text-white">
            <h3 className="text-2xl font-bold mb-6">Core Competencies</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              {[
                "Environmental Biotechnology",
                "Public Health Research", 
                "Laboratory Management",
                "Scientific Documentation"
              ].map((competency, index) => (
                <motion.div
                  key={competency}
                  initial={{ opacity: 0, scale: 0.8 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="text-center"
                >
                  <div className="w-16 h-16 bg-gradient-to-r from-teal-400 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-3">
                    <span className="text-2xl font-bold">{index + 1}</span>
                  </div>
                  <p className="text-sm font-medium">{competency}</p>
                </motion.div>
              ))}
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default SkillsSection;
