'use client';

import { motion } from 'framer-motion';
import { Mail, Phone, MapPin, Linkedin, Github } from 'lucide-react';

const Footer = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-slate-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {/* Contact Info */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="space-y-4"
          >
            <h3 className="text-xl font-bold text-teal-400 mb-4">Contact Information</h3>
            <div className="flex items-center space-x-3">
              <Mail size={18} className="text-teal-400" />
              <a 
                href="mailto:<EMAIL>"
                className="hover:text-teal-400 transition-colors"
              >
                <EMAIL>
              </a>
            </div>
            <div className="flex items-center space-x-3">
              <Phone size={18} className="text-teal-400" />
              <a 
                href="tel:+************"
                className="hover:text-teal-400 transition-colors"
              >
                +91 9691583220
              </a>
            </div>
            <div className="flex items-center space-x-3">
              <MapPin size={18} className="text-teal-400" />
              <span>Bengaluru, India</span>
            </div>
          </motion.div>

          {/* Quick Links */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="space-y-4"
          >
            <h3 className="text-xl font-bold text-teal-400 mb-4">Quick Links</h3>
            <div className="space-y-2">
              <a href="/" className="block hover:text-teal-400 transition-colors">Home</a>
              <a href="/about" className="block hover:text-teal-400 transition-colors">About</a>
              <a href="/experience" className="block hover:text-teal-400 transition-colors">Experience</a>
              <a href="/projects" className="block hover:text-teal-400 transition-colors">Projects</a>
              <a href="/contact" className="block hover:text-teal-400 transition-colors">Contact</a>
            </div>
          </motion.div>

          {/* Professional Summary */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="space-y-4"
          >
            <h3 className="text-xl font-bold text-teal-400 mb-4">About</h3>
            <p className="text-gray-300 text-sm leading-relaxed">
              Motivated biotechnology postgraduate with hands-on research experience at ICMR-NIV and ICMR-ROHC. 
              Passionate about solving public health challenges through biotechnology.
            </p>
            <div className="flex space-x-4 mt-4">
              <a 
                href="#" 
                className="text-gray-400 hover:text-teal-400 transition-colors"
                aria-label="LinkedIn"
              >
                <Linkedin size={20} />
              </a>
              <a 
                href="#" 
                className="text-gray-400 hover:text-teal-400 transition-colors"
                aria-label="GitHub"
              >
                <Github size={20} />
              </a>
            </div>
          </motion.div>
        </div>

        {/* Bottom Bar */}
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          className="border-t border-gray-700 mt-8 pt-8 text-center"
        >
          <p className="text-gray-400 text-sm">
            © {currentYear} Pratibha Madhukar. All rights reserved.
          </p>
        </motion.div>
      </div>
    </footer>
  );
};

export default Footer;
