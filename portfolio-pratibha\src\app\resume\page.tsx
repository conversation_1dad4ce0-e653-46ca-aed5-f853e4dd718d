'use client';

import { motion } from 'framer-motion';
import { Download, Mail, Phone, MapPin, Calendar, Building } from 'lucide-react';

export default function ResumePage() {
  return (
    <div className="min-h-screen pt-20 bg-gray-50">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-900 to-teal-400 py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center text-white">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">Resume</h1>
            <p className="text-xl text-white/90 mb-6">
              <PERSON><PERSON><PERSON><PERSON> - Biotechnologist & Research Professional
            </p>
            <button className="bg-white text-blue-900 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors flex items-center space-x-2 mx-auto">
              <Download size={20} />
              <span>Download PDF</span>
            </button>
          </div>
        </div>
      </div>

      {/* Resume Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="bg-white rounded-xl shadow-lg p-8"
        >
          {/* Personal Information */}
          <div className="text-center mb-8 pb-8 border-b border-gray-200">
            <h1 className="text-4xl font-bold text-gray-900 mb-2">Pratibha Madhukar</h1>
            <p className="text-xl text-teal-600 font-semibold mb-4">Biotechnologist & Research Professional</p>
            <div className="flex flex-wrap justify-center gap-6 text-gray-600">
              <div className="flex items-center space-x-2">
                <Mail size={16} />
                <span><EMAIL></span>
              </div>
              <div className="flex items-center space-x-2">
                <Phone size={16} />
                <span>+91 9691583220</span>
              </div>
              <div className="flex items-center space-x-2">
                <MapPin size={16} />
                <span>Bengaluru, India</span>
              </div>
            </div>
          </div>

          {/* Professional Summary */}
          <section className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-4 border-b-2 border-teal-400 pb-2">
              Professional Summary
            </h2>
            <p className="text-gray-700 leading-relaxed">
              Motivated biotechnology postgraduate with hands-on research experience at ICMR-NIV and ICMR-ROHC. 
              Skilled in ELISA, DNA/RNA extraction, microbial culture maintenance, and environmental biotechnology. 
              Passionate about solving public health challenges through biotechnology innovation and research excellence.
            </p>
          </section>

          {/* Experience */}
          <section className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-4 border-b-2 border-teal-400 pb-2">
              Professional Experience
            </h2>
            <div className="space-y-6">
              <div>
                <div className="flex justify-between items-start mb-2">
                  <div>
                    <h3 className="text-lg font-bold text-gray-900">Project Assistant</h3>
                    <p className="text-teal-600 font-semibold">ICMR-NIV, Bengaluru</p>
                  </div>
                  <span className="text-gray-600 text-sm">June 2025 – Present</span>
                </div>
                <ul className="list-disc list-inside text-gray-700 space-y-1 ml-4">
                  <li>Assisting in cutting-edge research projects focused on virology and public health</li>
                  <li>Conducting comprehensive data analysis and maintaining detailed research documentation</li>
                  <li>Collaborating with multidisciplinary teams on innovative biotechnology solutions</li>
                </ul>
              </div>

              <div>
                <div className="flex justify-between items-start mb-2">
                  <div>
                    <h3 className="text-lg font-bold text-gray-900">Research Intern</h3>
                    <p className="text-teal-600 font-semibold">ICMR-ROHC, Bengaluru</p>
                  </div>
                  <span className="text-gray-600 text-sm">January 2024 – June 2024</span>
                </div>
                <ul className="list-disc list-inside text-gray-700 space-y-1 ml-4">
                  <li>Conducted medical and occupational health research under DST-SERB funding</li>
                  <li>Gained hands-on experience with advanced laboratory techniques and equipment</li>
                  <li>Participated in research facility training programs and workshops</li>
                </ul>
              </div>
            </div>
          </section>

          {/* Education */}
          <section className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-4 border-b-2 border-teal-400 pb-2">
              Education
            </h2>
            <div className="space-y-4">
              <div className="flex justify-between items-start">
                <div>
                  <h3 className="text-lg font-bold text-gray-900">M.Sc Biotechnology</h3>
                  <p className="text-teal-600 font-semibold">St. Thomas College, Bhilai</p>
                </div>
                <div className="text-right">
                  <span className="text-gray-600 text-sm">2022 – 2024</span>
                  <p className="font-semibold text-gray-900">70%</p>
                </div>
              </div>

              <div className="flex justify-between items-start">
                <div>
                  <h3 className="text-lg font-bold text-gray-900">B.Sc Biotechnology</h3>
                  <p className="text-teal-600 font-semibold">St. Thomas College, Bhilai</p>
                </div>
                <div className="text-right">
                  <span className="text-gray-600 text-sm">2019 – 2022</span>
                  <p className="font-semibold text-gray-900">73.6%</p>
                </div>
              </div>
            </div>
          </section>

          {/* Skills */}
          <section className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-4 border-b-2 border-teal-400 pb-2">
              Technical Skills
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Laboratory Techniques</h3>
                <ul className="text-gray-700 space-y-1">
                  <li>• ELISA</li>
                  <li>• DNA/RNA Isolation</li>
                  <li>• ICP-OES</li>
                  <li>• Plant Tissue Culture</li>
                  <li>• Microbial Culture</li>
                  <li>• Microscopy</li>
                </ul>
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Software & Tools</h3>
                <ul className="text-gray-700 space-y-1">
                  <li>• MS Office Suite</li>
                  <li>• MS Excel (Advanced)</li>
                  <li>• PowerPoint</li>
                  <li>• R (Basic)</li>
                  <li>• Data Conversion</li>
                </ul>
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">Soft Skills</h3>
                <ul className="text-gray-700 space-y-1">
                  <li>• Time Management</li>
                  <li>• Organization</li>
                  <li>• Teamwork</li>
                  <li>• Problem Solving</li>
                  <li>• Communication</li>
                </ul>
              </div>
            </div>
          </section>

          {/* Certifications */}
          <section className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-4 border-b-2 border-teal-400 pb-2">
              Certifications & Workshops
            </h2>
            <ul className="space-y-2 text-gray-700">
              <li>• ICMR-ROHC Research Facility Training (DST-SERB) - 2024</li>
              <li>• International Workshop on Data Science & ML in R - 2024</li>
              <li>• International Workshop on Data Analytics in Biological Research - 2024</li>
            </ul>
          </section>

          {/* Research Projects */}
          <section>
            <h2 className="text-2xl font-bold text-gray-900 mb-4 border-b-2 border-teal-400 pb-2">
              Key Research Projects
            </h2>
            <ul className="space-y-2 text-gray-700">
              <li>• ELISA-ICP-OES Integration for Trace Metal Analysis</li>
              <li>• Virology & Public Health Research (ICMR-NIV)</li>
              <li>• Medical & Occupational Health Research (ICMR-ROHC)</li>
              <li>• Plant Tissue Culture & Environmental Biotechnology Studies</li>
            </ul>
          </section>
        </motion.div>
      </div>
    </div>
  );
}
