'use client';

import { motion } from 'framer-motion';
import { 
  FlaskConical, 
  Microscope, 
  BarChart3, 
  Leaf, 
  Shield, 
  ExternalLink,
  Calendar,
  Users
} from 'lucide-react';

const ProjectsSection = () => {
  const projects = [
    {
      title: "ELISA-ICP-OES Integration for Trace Metal Analysis",
      category: "Analytical Biotechnology",
      period: "2023 - 2024",
      status: "Completed",
      icon: <BarChart3 className="w-6 h-6" />,
      description: "Developed an integrated analytical approach combining ELISA and ICP-OES techniques for comprehensive trace metal analysis in biological samples.",
      highlights: [
        "Novel integration of ELISA and ICP-OES methodologies",
        "Enhanced sensitivity and specificity for trace metal detection",
        "Optimized protocols for biological sample analysis",
        "Comprehensive data validation and quality control"
      ],
      technologies: ["ELISA", "ICP-OES", "Data Analysis", "Quality Control"],
      impact: "Improved analytical accuracy by 25% compared to traditional methods"
    },
    {
      title: "Virology & Public Health Research",
      category: "Public Health",
      period: "2025 - Present",
      status: "Ongoing",
      icon: <Shield className="w-6 h-6" />,
      description: "Contributing to cutting-edge virology research at ICMR-NIV with focus on public health applications and disease prevention strategies.",
      highlights: [
        "Advanced virology research methodologies",
        "Public health impact assessment",
        "Collaborative research with multidisciplinary teams",
        "Data analysis and scientific documentation"
      ],
      technologies: ["Virology", "Public Health", "Data Analysis", "Research Documentation"],
      impact: "Contributing to national public health research initiatives"
    },
    {
      title: "Medical & Occupational Health Research",
      category: "Occupational Health",
      period: "2024",
      status: "Completed",
      icon: <Users className="w-6 h-6" />,
      description: "Conducted comprehensive research on medical and occupational health factors at ICMR-ROHC under DST-SERB funding.",
      highlights: [
        "Occupational health risk assessment",
        "Medical research data collection and analysis",
        "Environmental health factor evaluation",
        "Research facility training and skill development"
      ],
      technologies: ["Medical Research", "Occupational Health", "Environmental Assessment", "Statistical Analysis"],
      impact: "Enhanced understanding of occupational health risks in industrial settings"
    },
    {
      title: "Plant Tissue Culture & Environmental Biotechnology",
      category: "Environmental Biotechnology",
      period: "2022 - 2024",
      status: "Completed",
      icon: <Leaf className="w-6 h-6" />,
      description: "Extensive research in plant tissue culture techniques with applications in environmental biotechnology and sustainable agriculture.",
      highlights: [
        "Advanced plant tissue culture protocols",
        "Environmental biotechnology applications",
        "Sustainable agriculture solutions",
        "Microbial culture maintenance and optimization"
      ],
      technologies: ["Plant Tissue Culture", "Microbial Culture", "Environmental Biotechnology", "Sustainable Agriculture"],
      impact: "Developed optimized protocols for plant propagation and environmental remediation"
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Ongoing':
        return 'bg-green-100 text-green-800';
      case 'Completed':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <section className="section-padding bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Research <span className="text-gradient">Projects</span>
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-blue-900 to-teal-400 mx-auto mb-8"></div>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Innovative research projects driving scientific advancement and practical solutions
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {projects.map((project, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.2 }}
              viewport={{ once: true }}
              className="card-hover bg-gradient-to-br from-gray-50 to-white rounded-xl p-6 shadow-lg border border-gray-100 group"
            >
              {/* Header */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="p-3 bg-gradient-to-r from-blue-900 to-teal-400 rounded-lg text-white">
                    {project.icon}
                  </div>
                  <div>
                    <span className="text-sm font-medium text-teal-600 uppercase tracking-wide">
                      {project.category}
                    </span>
                  </div>
                </div>
                <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(project.status)}`}>
                  {project.status}
                </span>
              </div>

              {/* Title and Period */}
              <h3 className="text-xl font-bold text-gray-900 mb-2 group-hover:text-teal-600 transition-colors">
                {project.title}
              </h3>
              
              <div className="flex items-center text-gray-600 text-sm mb-4">
                <Calendar size={14} className="mr-1" />
                {project.period}
              </div>

              {/* Description */}
              <p className="text-gray-700 mb-4 leading-relaxed">
                {project.description}
              </p>

              {/* Highlights */}
              <div className="mb-4">
                <h4 className="text-sm font-semibold text-gray-900 mb-2">Key Highlights:</h4>
                <ul className="space-y-1">
                  {project.highlights.map((highlight, idx) => (
                    <li key={idx} className="text-sm text-gray-600 flex items-start">
                      <span className="w-1.5 h-1.5 bg-teal-400 rounded-full mt-2 mr-2 flex-shrink-0"></span>
                      {highlight}
                    </li>
                  ))}
                </ul>
              </div>

              {/* Technologies */}
              <div className="mb-4">
                <div className="flex flex-wrap gap-2">
                  {project.technologies.map((tech, idx) => (
                    <span
                      key={idx}
                      className="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-xs font-medium"
                    >
                      {tech}
                    </span>
                  ))}
                </div>
              </div>

              {/* Impact */}
              <div className="border-t border-gray-100 pt-4">
                <div className="flex items-start space-x-2">
                  <BarChart3 size={16} className="text-teal-500 mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="text-sm font-semibold text-gray-900 mb-1">Impact:</h4>
                    <p className="text-sm text-gray-600">{project.impact}</p>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Research Areas Summary */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          viewport={{ once: true }}
          className="mt-16"
        >
          <div className="bg-gradient-to-r from-gray-900 to-gray-800 rounded-2xl p-8 text-white">
            <h3 className="text-2xl font-bold mb-6 text-center">Research Focus Areas</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {[
                { icon: <Microscope className="w-8 h-8" />, title: "Analytical Methods", count: "4+" },
                { icon: <Shield className="w-8 h-8" />, title: "Public Health", count: "2+" },
                { icon: <Leaf className="w-8 h-8" />, title: "Environmental", count: "3+" },
                { icon: <FlaskConical className="w-8 h-8" />, title: "Laboratory Research", count: "5+" }
              ].map((area, index) => (
                <motion.div
                  key={area.title}
                  initial={{ opacity: 0, scale: 0.8 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="text-center"
                >
                  <div className="text-teal-400 mb-3 flex justify-center">
                    {area.icon}
                  </div>
                  <div className="text-2xl font-bold mb-1">{area.count}</div>
                  <p className="text-sm text-gray-300">{area.title}</p>
                </motion.div>
              ))}
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default ProjectsSection;
