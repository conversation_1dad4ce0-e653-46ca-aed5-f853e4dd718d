'use client';

import { motion } from 'framer-motion';
import { Microscope, FlaskConical, Dna, Users } from 'lucide-react';

const AboutSection = () => {
  const highlights = [
    {
      icon: <Microscope className="w-8 h-8" />,
      title: "Research Experience",
      description: "Hands-on experience at ICMR-NIV and ICMR-ROHC in cutting-edge biotechnology research"
    },
    {
      icon: <FlaskConical className="w-8 h-8" />,
      title: "Laboratory Skills",
      description: "Proficient in ELISA, DNA/RNA extraction, microbial culture maintenance, and analytical techniques"
    },
    {
      icon: <Dna className="w-8 h-8" />,
      title: "Biotechnology Focus",
      description: "Specialized in environmental biotechnology and public health applications"
    },
    {
      icon: <Users className="w-8 h-8" />,
      title: "Collaborative Approach",
      description: "Strong teamwork and communication skills developed through research collaborations"
    }
  ];

  return (
    <section id="about" className="section-padding bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            About <span className="text-gradient">Me</span>
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-blue-900 to-teal-400 mx-auto mb-8"></div>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Left Column - Text Content */}
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="space-y-6"
          >
            <div className="prose prose-lg max-w-none">
              <p className="text-gray-700 leading-relaxed text-lg">
                I am a motivated biotechnology postgraduate with comprehensive research experience at prestigious institutions including{' '}
                <span className="font-semibold text-blue-900">ICMR-NIV</span> and{' '}
                <span className="font-semibold text-blue-900">ICMR-ROHC</span>. My expertise spans across various domains of biotechnology, with a particular focus on environmental applications and public health research.
              </p>
              
              <p className="text-gray-700 leading-relaxed text-lg">
                Currently serving as a Project Assistant at ICMR-NIV Bengaluru, I am passionate about leveraging biotechnology to address real-world challenges. My technical proficiency includes advanced laboratory techniques such as ELISA, ICP-OES, DNA/RNA isolation, and plant tissue culture.
              </p>

              <p className="text-gray-700 leading-relaxed text-lg">
                I believe in the power of interdisciplinary collaboration and continuous learning to drive innovation in biotechnology research. My goal is to contribute meaningfully to scientific advancement while solving critical public health challenges.
              </p>
            </div>

            <div className="flex flex-wrap gap-3 pt-4">
              {['Biotechnology', 'Research', 'ELISA', 'DNA/RNA Extraction', 'Microbiology', 'Data Analysis'].map((skill, index) => (
                <motion.span
                  key={skill}
                  initial={{ opacity: 0, scale: 0.8 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="px-4 py-2 bg-gradient-to-r from-blue-900 to-teal-400 text-white rounded-full text-sm font-medium"
                >
                  {skill}
                </motion.span>
              ))}
            </div>
          </motion.div>

          {/* Right Column - Highlights Grid */}
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="grid grid-cols-1 sm:grid-cols-2 gap-6"
          >
            {highlights.map((highlight, index) => (
              <motion.div
                key={highlight.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="card-hover bg-gradient-to-br from-gray-50 to-white p-6 rounded-xl border border-gray-100 shadow-sm"
              >
                <div className="text-teal-500 mb-4">
                  {highlight.icon}
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {highlight.title}
                </h3>
                <p className="text-gray-600 text-sm leading-relaxed">
                  {highlight.description}
                </p>
              </motion.div>
            ))}
          </motion.div>
        </div>

        {/* Current Position Highlight */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          viewport={{ once: true }}
          className="mt-16 text-center"
        >
          <div className="bg-gradient-to-r from-blue-900 to-teal-400 rounded-2xl p-8 text-white">
            <h3 className="text-2xl font-bold mb-4">Current Position</h3>
            <p className="text-xl mb-2">Project Assistant</p>
            <p className="text-teal-200 text-lg">ICMR-NIV, Bengaluru</p>
            <p className="text-white/90 mt-4 max-w-2xl mx-auto">
              Contributing to cutting-edge research projects, data analysis, and documentation in virology and public health research
            </p>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default AboutSection;
