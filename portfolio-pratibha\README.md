# <PERSON><PERSON><PERSON><PERSON> - Portfolio Website

A modern, professional portfolio website for **<PERSON><PERSON><PERSON><PERSON>**, a biotechnology and research professional with expertise in ICMR research, laboratory techniques, and environmental biotechnology.

## 🌟 Features

- **Modern Design**: Clean, scientific-inspired design with professional color scheme
- **Responsive**: Fully responsive design that works on all devices
- **Interactive**: Smooth animations and transitions using Framer Motion
- **SEO Optimized**: Proper meta tags and structured data
- **Fast Performance**: Built with Next.js 15 and optimized for speed

## 🎨 Design Theme

- **Color Palette**: Blue (#1a2e45), <PERSON><PERSON> (#4ecdc4), White (#f5f5f5)
- **Typography**: <PERSON><PERSON><PERSON> (headings), <PERSON><PERSON> (body text)
- **Style**: Clean, minimal, science-inspired with subtle DNA/molecule patterns
- **Animations**: Smooth transitions and floating elements

## 📱 Pages & Sections

### Home Page
- **Hero Section**: Animated introduction with rotating titles
- **About Me**: Professional summary and highlights
- **Experience**: Timeline-style work experience
- **Skills**: Interactive skill cards with progress bars
- **Education**: Academic qualifications and certifications
- **Projects**: Research projects showcase

### Additional Pages
- **About**: Detailed about page with skills and education
- **Experience**: Dedicated experience timeline
- **Projects**: Comprehensive research projects gallery
- **Contact**: Contact form and information
- **Resume**: Downloadable resume page

## 🛠️ Tech Stack

- **Framework**: Next.js 15 with App Router
- **Styling**: Tailwind CSS v4
- **Animations**: Framer Motion
- **Icons**: Lucide React
- **Language**: TypeScript
- **Deployment**: Ready for Vercel deployment

## 🚀 Getting Started

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd portfolio-pratibha
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Run the development server**
   ```bash
   npm run dev
   ```

4. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 📂 Project Structure

```
portfolio-pratibha/
├── src/
│   ├── app/                    # Next.js App Router pages
│   │   ├── about/             # About page
│   │   ├── contact/           # Contact page
│   │   ├── experience/        # Experience page
│   │   ├── projects/          # Projects page
│   │   ├── resume/            # Resume page
│   │   ├── layout.tsx         # Root layout
│   │   ├── page.tsx           # Home page
│   │   └── globals.css        # Global styles
│   └── components/            # Reusable components
│       ├── Navigation.tsx     # Navigation bar
│       ├── Footer.tsx         # Footer component
│       ├── HeroSection.tsx    # Hero section
│       ├── AboutSection.tsx   # About section
│       ├── ExperienceSection.tsx # Experience timeline
│       ├── SkillsSection.tsx  # Skills showcase
│       ├── EducationSection.tsx # Education & certifications
│       └── ProjectsSection.tsx # Projects gallery
├── public/                    # Static assets
└── package.json              # Dependencies
```

## 🎯 Key Highlights

### Professional Experience
- **ICMR-NIV, Bengaluru** - Project Assistant (2025-Present)
- **ICMR-ROHC, Bengaluru** - Research Intern (2024)
- **Academic Research Projects** - Various biotechnology research

### Technical Skills
- **Laboratory**: ELISA, DNA/RNA Isolation, ICP-OES, Plant Tissue Culture
- **Software**: MS Office, R (Basic), Data Analysis
- **Research**: Environmental Biotechnology, Public Health, Virology

### Education
- **M.Sc Biotechnology** - St. Thomas College, Bhilai (70%)
- **B.Sc Biotechnology** - St. Thomas College, Bhilai (73.6%)

## 🚀 Deployment

### Deploy on Vercel (Recommended)

1. Push your code to GitHub
2. Connect your repository to Vercel
3. Deploy with one click

### Manual Deployment

```bash
npm run build
npm start
```

## 📞 Contact Information

- **Email**: <EMAIL>
- **Phone**: +91 **********
- **Location**: Bengaluru, India

## 📄 License

This project is created for Pratibha Madhukar's professional portfolio. All rights reserved.

---

Built with ❤️ using Next.js, Tailwind CSS, and Framer Motion
